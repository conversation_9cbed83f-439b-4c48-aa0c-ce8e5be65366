import type { NavItem, FeatureItem, TestimonialItem } from '../types';

export const navigationItems: NavItem[] = [
  { label: 'Home', href: '/' },
  { label: 'About', href: '/about' },
  { label: 'Features', href: '#features' },
  { label: 'Testimonials', href: '#testimonials' },
  { label: 'Contact', href: '/contact' },
];

export const features: FeatureItem[] = [
  {
    id: '1',
    title: 'Speaking & Pronunciation',
    description: 'Master English pronunciation with AI-powered speech recognition and real-time feedback from native speakers.',
    icon: '🎯',
  },
  {
    id: '2',
    title: 'Grammar Mastery',
    description: 'Build strong grammar foundations with interactive lessons covering all English tenses and structures.',
    icon: '📚',
  },
  {
    id: '3',
    title: 'Vocabulary Building',
    description: 'Expand your vocabulary with contextual learning, spaced repetition, and real-world usage examples.',
    icon: '⚡',
  },
  {
    id: '4',
    title: 'Listening Comprehension',
    description: 'Improve your listening skills with authentic audio content from various English-speaking regions.',
    icon: '📊',
  },
  {
    id: '5',
    title: 'Writing Skills',
    description: 'Develop professional writing abilities through guided practice and automated feedback systems.',
    icon: '👥',
  },
  {
    id: '6',
    title: 'Cultural Context',
    description: 'Learn English within cultural contexts to communicate effectively in real-world situations.',
    icon: '📱',
  },
];

export const testimonials: TestimonialItem[] = [
  {
    id: '1',
    name: 'Maria Rodriguez',
    role: 'Business Student',
    company: 'University of Madrid',
    content: 'This ESL platform has completely transformed my English learning journey! The interactive lessons and personalized feedback helped me gain confidence in speaking. I went from being afraid to speak in class to actively participating in discussions.',
    rating: 5,
    avatar: 'https://cdn.pixabay.com/photo/2017/06/26/02/47/man-2442565_1280.jpg',
  },
  {
    id: '2',
    name: 'Ahmed Hassan',
    role: 'Software Engineer',
    company: 'Tech Solutions Inc.',
    content: 'As a working professional, I needed flexible learning options that fit my busy schedule. This platform provided exactly what I needed to advance my career. The pronunciation tools helped me communicate better with international clients.',
    rating: 5,
    avatar: 'https://cdn.pixabay.com/photo/2016/11/21/12/42/beard-1845166_1280.jpg',
  },
  {
    id: '3',
    name: 'Li Wei',
    role: 'Graduate Student',
    company: 'MIT',
    content: 'The pronunciation tools and real-time feedback have been invaluable for my academic presentations and daily communication. I can now present my research confidently to international audiences.',
    rating: 5,
    avatar: 'https://cdn.pixabay.com/photo/2017/08/01/01/33/beanie-2562646_1280.jpg',
  },
  {
    id: '4',
    name: 'Sarah Johnson',
    role: 'Marketing Manager',
    company: 'Global Marketing Co.',
    content: 'The cultural context lessons were a game-changer for me. Understanding not just the language but also the cultural nuances has made me much more effective in international business meetings.',
    rating: 5,
    avatar: 'https://cdn.pixabay.com/photo/2017/05/31/04/59/beautiful-2359121_1280.jpg',
  },
  {
    id: '5',
    name: 'Carlos Mendoza',
    role: 'Medical Student',
    company: 'Johns Hopkins University',
    content: 'The medical English vocabulary modules were exactly what I needed for my studies. The platform helped me master complex medical terminology and communicate effectively with patients during my rotations.',
    rating: 4,
    avatar: 'https://cdn.pixabay.com/photo/2016/11/29/09/38/adult-1868750_1280.jpg',
  },
  {
    id: '6',
    name: 'Priya Patel',
    role: 'Data Analyst',
    company: 'Analytics Pro',
    content: 'The business English course helped me excel in my new role. I can now present data insights clearly and participate confidently in team meetings. The improvement in my communication skills has been remarkable!',
    rating: 5,
    avatar: 'https://cdn.pixabay.com/photo/2017/08/06/12/06/people-2591874_1280.jpg',
  },
];

export const companyStats = [
  { label: 'Students Enrolled', value: '10,000+' },
  { label: 'Lessons Completed', value: '500,000+' },
  { label: 'Success Rate', value: '95%' },
  { label: 'Countries Served', value: '50+' },
];
