import React from 'react';
import { cn } from '../../utils';

interface CoursesSectionProps {
  className?: string;
}

interface CourseCategory {
  id: string;
  title: string;
  courseCount: number;
  color: string;
  size: 'small' | 'medium' | 'large';
  image: string;
  description: string;
}

const courseCategories: CourseCategory[] = [
  {
    id: '1',
    title: 'PROGRAMMING',
    courseCount: 22,
    color: 'bg-gradient-to-br from-blue-500 to-purple-600',
    size: 'large',
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop&crop=center',
    description: 'Learn coding fundamentals and advanced programming concepts',
  },
  {
    id: '2',
    title: 'DEVELOPMENT',
    courseCount: 15,
    color: 'bg-gradient-to-br from-green-400 to-green-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-**********-4365d14bab8c?w=400&h=300&fit=crop&crop=center',
    description: 'Web and mobile app development skills',
  },
  {
    id: '3',
    title: 'HEALTH',
    courseCount: 78,
    color: 'bg-gradient-to-br from-purple-500 to-purple-700',
    size: 'large',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop&crop=center',
    description: 'Wellness, fitness, and healthcare education',
  },
  {
    id: '4',
    title: 'INTERIOR',
    courseCount: 31,
    color: 'bg-gradient-to-br from-blue-400 to-blue-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=center',
    description: 'Interior design and home decoration',
  },
  {
    id: '5',
    title: 'MUSIC',
    courseCount: 12,
    color: 'bg-gradient-to-br from-orange-400 to-orange-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop&crop=center',
    description: 'Music theory, instruments, and production',
  },
  {
    id: '6',
    title: 'BUSINESS',
    courseCount: 34,
    color: 'bg-gradient-to-br from-blue-500 to-blue-700',
    size: 'large',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=center',
    description: 'Entrepreneurship and business management',
  },
  {
    id: '7',
    title: 'DESIGN',
    courseCount: 84,
    color: 'bg-gradient-to-br from-red-400 to-red-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=300&fit=crop&crop=center',
    description: 'Graphic design and creative arts',
  },
  {
    id: '8',
    title: 'PHOTOGRAPHY',
    courseCount: 71,
    color: 'bg-gradient-to-br from-pink-400 to-pink-600',
    size: 'medium',
    image: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop&crop=center',
    description: 'Photography techniques and visual storytelling',
  },
];

const getSizeClasses = (size: string) => {
  switch (size) {
    case 'small':
      return 'h-32 w-full';
    case 'medium':
      return 'h-40 w-full';
    case 'large':
      return 'h-48 w-full';
    default:
      return 'h-32 w-full';
  }
};

const CourseCard: React.FC<{ category: CourseCategory }> = ({ category }) => (
  <div className={cn(
    'rounded-lg text-white overflow-hidden cursor-pointer transform hover:scale-[1.02] transition-all duration-300 shadow-md hover:shadow-lg relative group',
    getSizeClasses(category.size)
  )}>
    <div className="absolute inset-0">
      <img
        src={category.image}
        alt={category.title}
        className="w-full h-full object-cover"
      />
      <div className={cn('absolute inset-0 opacity-80 group-hover:opacity-70 transition-opacity', category.color)}></div>
    </div>
    <div className="relative z-10 p-3 h-full flex flex-col justify-end">
      <h3 className="text-sm font-bold mb-1 leading-tight">{category.title}</h3>
      <p className="text-xs opacity-90">{category.courseCount} Courses</p>
    </div>
  </div>
);

export const CoursesSection: React.FC<CoursesSectionProps> = ({ className }) => {
  return (
    <section className={cn('py-20 relative overflow-hidden', className)}>
      {/* Enhanced Thematic Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50">
        {/* Learning Category Icons */}
        <div className="absolute top-16 left-12 opacity-8 text-5xl font-bold text-emerald-600 transform rotate-12">
          💻
        </div>
        <div className="absolute top-32 right-16 opacity-8 text-4xl font-bold text-teal-600 transform -rotate-6">
          🎨
        </div>
        <div className="absolute bottom-40 left-24 opacity-8 text-5xl font-bold text-cyan-500 transform rotate-6">
          📚
        </div>
        <div className="absolute bottom-24 right-20 opacity-8 text-4xl font-bold text-emerald-500 transform -rotate-12">
          🎵
        </div>
        <div className="absolute top-1/2 left-1/4 opacity-8 text-3xl font-bold text-teal-400 transform rotate-45">
          📸
        </div>
        <div className="absolute top-1/3 right-1/3 opacity-8 text-4xl font-bold text-cyan-400 transform -rotate-30">
          🏥
        </div>

        {/* Knowledge Network Lines */}
        <div className="absolute top-20 left-20 w-24 h-0.5 bg-gradient-to-r from-emerald-200 to-transparent opacity-30 transform rotate-45"></div>
        <div className="absolute bottom-32 right-24 w-32 h-0.5 bg-gradient-to-l from-teal-200 to-transparent opacity-30 transform -rotate-45"></div>
        <div className="absolute top-1/2 left-1/3 w-20 h-0.5 bg-gradient-to-r from-cyan-200 to-transparent opacity-30 transform rotate-12"></div>

        {/* Geometric Learning Elements */}
        <div className="absolute top-24 right-12 w-28 h-28 border-2 border-emerald-200 rounded-full opacity-20"></div>
        <div className="absolute bottom-36 left-20 w-20 h-20 border-2 border-teal-200 rounded-full opacity-20"></div>
        <div className="absolute top-1/3 right-1/4 w-14 h-14 bg-emerald-100 rounded-lg opacity-30 transform rotate-45"></div>
        <div className="absolute bottom-1/4 left-1/3 w-18 h-18 bg-teal-100 rounded-lg opacity-30 transform -rotate-12"></div>

        {/* Progress Dots */}
        <div className="absolute top-20 left-1/2 w-6 h-6 bg-emerald-200 rounded-full opacity-20"></div>
        <div className="absolute top-24 left-1/2 ml-2 w-5 h-5 bg-emerald-200 rounded-full opacity-15"></div>
        <div className="absolute top-28 left-1/2 ml-4 w-4 h-4 bg-emerald-200 rounded-full opacity-10"></div>

        <div className="absolute bottom-28 right-1/3 w-6 h-6 bg-teal-200 rounded-full opacity-20"></div>
        <div className="absolute bottom-32 right-1/3 mr-2 w-5 h-5 bg-teal-200 rounded-full opacity-15"></div>
        <div className="absolute bottom-36 right-1/3 mr-4 w-4 h-4 bg-teal-200 rounded-full opacity-10"></div>

        {/* Skill Development Symbols */}
        <div className="absolute top-1/4 left-12 opacity-12 text-2xl">⚡</div>
        <div className="absolute top-3/4 right-20 opacity-12 text-2xl">🚀</div>
        <div className="absolute bottom-1/3 left-1/4 opacity-12 text-2xl">🎯</div>
        <div className="absolute top-1/2 right-1/4 opacity-12 text-2xl">💡</div>
        <div className="absolute bottom-1/2 left-1/2 opacity-12 text-2xl">🌟</div>
      </div>

      <div className="container-custom relative z-10">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/80 backdrop-blur-sm rounded-full mb-6 shadow-lg border border-emerald-100">
            <svg className="w-8 h-8 text-emerald-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <p className="text-sm font-semibold text-emerald-600 uppercase tracking-wider mb-4 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full inline-block">
            EXPAND YOUR HORIZONS
          </p>
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 font-heading mb-4">
            Explore Learning Categories
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Discover diverse learning opportunities beyond English. From programming to photography,
            expand your skillset with expert-led courses designed for global learners.
          </p>
        </div>

        {/* Enhanced Courses Grid Container */}
        <div className="max-w-5xl mx-auto relative">
          {/* Subtle Grid Background Enhancement */}
          <div className="absolute inset-0 bg-white/30 backdrop-blur-sm rounded-3xl border border-white/50 shadow-xl -m-4"></div>

          <div className="relative z-10 p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
              {/* Column 1 */}
              <div className="space-y-3 md:space-y-4">
                <CourseCard category={courseCategories[0]} />
                <CourseCard category={courseCategories[4]} />
              </div>

              {/* Column 2 */}
              <div className="space-y-3 md:space-y-4">
                <CourseCard category={courseCategories[1]} />
                <CourseCard category={courseCategories[5]} />
              </div>

              {/* Column 3 */}
              <div className="space-y-3 md:space-y-4">
                <CourseCard category={courseCategories[2]} />
                <CourseCard category={courseCategories[6]} />
              </div>

              {/* Column 4 */}
              <div className="space-y-3 md:space-y-4">
                <CourseCard category={courseCategories[3]} />
                <CourseCard category={courseCategories[7]} />
              </div>
            </div>
          </div>
        </div>

        {/* Learning Path Indicator */}
        <div className="text-center mt-12">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-200 to-teal-200 rounded-full blur-lg opacity-30 animate-pulse"></div>
            <div className="relative bg-white/90 backdrop-blur-sm rounded-full px-6 py-3 border border-emerald-200 shadow-lg">
              <div className="flex items-center gap-3 text-emerald-700 font-medium">
                <span className="text-lg">🎓</span>
                <span>Start your learning journey today</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
