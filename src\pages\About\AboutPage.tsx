import React from 'react';
import { Layout } from '../../components/layout';
import { <PERSON>, Button } from '../../components/ui';
import type { PageProps } from '../../types';
import { companyStats } from '../../data';

// SVG Icon Components for Values with Colors
const ExcellenceIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="url(#excellence-gradient)" stroke="#10B981" strokeWidth="2"/>
    <path d="M9 12l2 2 4-4" stroke="#FFFFFF" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
    <defs>
      <linearGradient id="excellence-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#10B981"/>
        <stop offset="100%" stopColor="#059669"/>
      </linearGradient>
    </defs>
  </svg>
);

const AccessibilityIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="9" cy="7" r="3" fill="#3B82F6" fillOpacity="0.8"/>
    <circle cx="15" cy="7" r="3" fill="#06B6D4" fillOpacity="0.8"/>
    <circle cx="18" cy="10" r="2" fill="#8B5CF6" fillOpacity="0.8"/>
    <path d="M1 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="#3B82F6" strokeWidth="2.5" strokeLinecap="round"/>
    <path d="M15 21v-2a4 4 0 0 1 4-4h2" stroke="#06B6D4" strokeWidth="2.5" strokeLinecap="round"/>
    <circle cx="12" cy="16" r="1" fill="#F59E0B"/>
  </svg>
);

const InnovationIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="5" fill="url(#innovation-gradient)"/>
    <path d="M12 2v2M12 20v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M2 12h2M20 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" stroke="#F59E0B" strokeWidth="2" strokeLinecap="round"/>
    <circle cx="12" cy="12" r="3" fill="#FFFFFF"/>
    <circle cx="12" cy="12" r="1" fill="#F59E0B"/>
    <defs>
      <radialGradient id="innovation-gradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#FCD34D"/>
        <stop offset="100%" stopColor="#F59E0B"/>
      </radialGradient>
    </defs>
  </svg>
);

export const AboutPage: React.FC<PageProps> = ({ className }) => {
  return (
    <Layout className={className}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-900 font-heading mb-6">
              About ESL Learn
            </h1>
            <p className="text-xl text-secondary-600 mb-8">
              We're passionate about making English language learning accessible, 
              engaging, and effective for students around the world.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-secondary-900 font-heading mb-6">
                Our Mission
              </h2>
              <p className="text-lg text-secondary-600 mb-6">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod
                tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
                quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
                consequat.
              </p>
              <p className="text-lg text-secondary-600 mb-8">
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum
                dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non
                proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
              </p>
              <Button size="lg">Learn More About Our Approach</Button>
            </div>
            <div className="relative">
              <div className="bg-primary-100 rounded-2xl p-8">
                <div className="grid grid-cols-2 gap-6">
                  {companyStats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-2xl font-bold text-primary-600 mb-2">
                        {stat.value}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 font-heading mb-4">
              Our Values
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              These core values guide everything we do and shape the learning experience we provide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <ExcellenceIcon />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                Excellence
              </h3>
              <p className="text-secondary-600">
                We strive for excellence in every aspect of our platform, from content quality
                to user experience.
              </p>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <AccessibilityIcon />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                Accessibility
              </h3>
              <p className="text-secondary-600">
                Quality English education should be accessible to everyone, regardless of
                background or location.
              </p>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <InnovationIcon />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                Innovation
              </h3>
              <p className="text-secondary-600">
                We continuously innovate to create more effective and engaging learning
                experiences.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 md:p-12 text-center">
            <h3 className="text-2xl md:text-3xl font-bold text-secondary-900 mb-4">
              Ready to Start Your Learning Journey?
            </h3>
            <p className="text-lg text-secondary-600 mb-8 max-w-2xl mx-auto">
              Join thousands of students who have already improved their English skills 
              with our comprehensive learning platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8 py-3">
                Get Started Today
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-3">
                Contact Us
              </Button>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};
