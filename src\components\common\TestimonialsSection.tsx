import React, { useState } from 'react';
import { Card } from '../ui';
import { testimonials } from '../../data';
import { cn } from '../../utils';

interface TestimonialsSectionProps {
  className?: string;
}

export const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({ className }) => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <svg
        key={index}
        className={cn(
          'w-4 h-4 transition-colors duration-200',
          index < rating ? 'text-yellow-400' : 'text-secondary-300'
        )}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <section id="testimonials" className={cn('py-20 relative overflow-hidden', className)}>
      {/* Enhanced Thematic Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        {/* Floating Language Elements */}
        <div className="absolute top-10 left-10 opacity-10 text-6xl font-bold text-primary-600 transform rotate-12">
          Hello
        </div>
        <div className="absolute top-32 right-20 opacity-10 text-4xl font-bold text-secondary-600 transform -rotate-6">
          Hola
        </div>
        <div className="absolute bottom-40 left-20 opacity-10 text-5xl font-bold text-primary-500 transform rotate-6">
          Bonjour
        </div>
        <div className="absolute bottom-20 right-32 opacity-10 text-4xl font-bold text-secondary-500 transform -rotate-12">
          こんにちは
        </div>
        <div className="absolute top-1/2 left-1/4 opacity-10 text-3xl font-bold text-primary-400 transform rotate-45">
          Guten Tag
        </div>

        {/* Geometric Patterns */}
        <div className="absolute top-20 right-10 w-32 h-32 border-2 border-primary-200 rounded-full opacity-20"></div>
        <div className="absolute bottom-32 left-16 w-24 h-24 border-2 border-secondary-200 rounded-full opacity-20"></div>
        <div className="absolute top-1/3 right-1/4 w-16 h-16 bg-primary-100 rounded-lg opacity-30 transform rotate-45"></div>
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-secondary-100 rounded-lg opacity-30 transform -rotate-12"></div>

        {/* Speech Bubbles */}
        <div className="absolute top-16 left-1/2 w-8 h-8 bg-primary-200 rounded-full opacity-20"></div>
        <div className="absolute top-20 left-1/2 ml-2 w-6 h-6 bg-primary-200 rounded-full opacity-15"></div>
        <div className="absolute top-24 left-1/2 ml-4 w-4 h-4 bg-primary-200 rounded-full opacity-10"></div>

        <div className="absolute bottom-24 right-1/3 w-8 h-8 bg-secondary-200 rounded-full opacity-20"></div>
        <div className="absolute bottom-28 right-1/3 mr-2 w-6 h-6 bg-secondary-200 rounded-full opacity-15"></div>
        <div className="absolute bottom-32 right-1/3 mr-4 w-4 h-4 bg-secondary-200 rounded-full opacity-10"></div>

        {/* Learning Icons */}
        <div className="absolute top-1/4 left-10 opacity-15 text-3xl">📚</div>
        <div className="absolute top-3/4 right-16 opacity-15 text-3xl">🎯</div>
        <div className="absolute bottom-1/3 left-1/4 opacity-15 text-3xl">💬</div>
        <div className="absolute top-1/2 right-1/4 opacity-15 text-3xl">🌟</div>
      </div>

      <div className="container-custom relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-white/80 backdrop-blur-sm rounded-full mb-6 shadow-lg border border-primary-100">
            <svg className="w-10 h-10 text-primary-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 font-heading mb-6">
            Success Stories from Our Students
          </h2>
          <p className="text-xl text-secondary-600 max-w-4xl mx-auto leading-relaxed">
            Join thousands of students who have transformed their English skills and achieved their goals.
            Here's what they have to say about their learning journey with us.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16 max-w-6xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              className={cn(
                'relative group hover:shadow-2xl hover:-translate-y-3 transition-all duration-500 cursor-pointer overflow-hidden',
                'border-0 bg-white/90 backdrop-blur-md shadow-xl',
                index === 1 ? 'lg:mt-8' : '', // Center card elevated
                'hover:bg-white/95'
              )}
              variant="elevated"
              onMouseEnter={() => setHoveredCard(testimonial.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              {/* Enhanced Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-blue-500/5 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              {/* Decorative Corner Elements */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary-100/50 to-transparent rounded-bl-full opacity-50"></div>
              <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-secondary-100/50 to-transparent rounded-tr-full opacity-50"></div>

              {/* Quote Icon */}
              <div className="absolute top-6 right-6 text-primary-300 group-hover:text-primary-500 transition-colors duration-300 group-hover:scale-110">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>

              {/* Content */}
              <div className="relative z-10 p-8">
                {/* Rating */}
                <div className="flex items-center gap-1 mb-6">
                  {renderStars(testimonial.rating)}
                  <span className="ml-2 text-sm font-medium text-secondary-600 bg-secondary-100 px-2 py-1 rounded-full">
                    {testimonial.rating}.0
                  </span>
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-secondary-700 leading-relaxed mb-8 text-base font-medium">
                  "{testimonial.content}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center">
                  <div className="relative">
                    {testimonial.avatar ? (
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-14 h-14 rounded-full object-cover border-3 border-white shadow-lg group-hover:scale-110 transition-transform duration-300 ring-2 ring-primary-100"
                        onError={(e) => {
                          // Fallback to initials if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.nextElementSibling as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div
                      className={cn(
                        'w-14 h-14 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center shadow-lg ring-2 ring-primary-100',
                        testimonial.avatar ? 'hidden' : 'flex'
                      )}
                    >
                      <span className="text-white font-bold text-lg">
                        {testimonial.name.charAt(0)}
                      </span>
                    </div>
                    {/* Success indicator */}
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="font-bold text-secondary-900 text-base group-hover:text-primary-600 transition-colors duration-300">
                      {testimonial.name}
                    </div>
                    <div className="text-secondary-600 text-sm font-medium">
                      {testimonial.role}
                    </div>
                    <div className="text-secondary-500 text-xs bg-secondary-50 px-2 py-1 rounded-full inline-block mt-1">
                      {testimonial.company}
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Hover Effect Border */}
              <div className="absolute inset-0 border-2 border-gradient-to-r from-primary-300 to-blue-300 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              {/* Learning Achievement Badge */}
              <div className="absolute top-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="bg-green-100 text-green-700 text-xs font-semibold px-2 py-1 rounded-full flex items-center gap-1">
                  <span>✓</span>
                  <span>Verified Success</span>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Enhanced Stats Section */}
        <div className="relative bg-white/80 backdrop-blur-md rounded-3xl p-8 border border-white/50 shadow-2xl overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary-50/50 via-blue-50/30 to-purple-50/50"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary-100/30 to-transparent rounded-bl-full"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-secondary-100/30 to-transparent rounded-tr-full"></div>

          <div className="relative z-10 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="group cursor-pointer">
              <div className="relative">
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                  10,000+
                </div>
                <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span className="text-yellow-400 text-lg">🎉</span>
                </div>
              </div>
              <div className="text-secondary-600 font-medium">Happy Students</div>
              <div className="w-12 h-1 bg-gradient-to-r from-primary-400 to-blue-400 rounded-full mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="group cursor-pointer">
              <div className="relative">
                <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                  98%
                </div>
                <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span className="text-green-400 text-lg">✅</span>
                </div>
              </div>
              <div className="text-secondary-600 font-medium">Success Rate</div>
              <div className="w-12 h-1 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="group cursor-pointer">
              <div className="relative">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                  50+
                </div>
                <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span className="text-blue-400 text-lg">🌍</span>
                </div>
              </div>
              <div className="text-secondary-600 font-medium">Countries</div>
              <div className="w-12 h-1 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="group cursor-pointer">
              <div className="relative">
                <div className="text-3xl md:text-4xl font-bold text-yellow-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                  4.9★
                </div>
                <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span className="text-yellow-400 text-lg">⭐</span>
                </div>
              </div>
              <div className="text-secondary-600 font-medium">Average Rating</div>
              <div className="w-12 h-1 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        </div>

        {/* Enhanced Bottom CTA */}
        <div className="text-center mt-16">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-200 to-blue-200 rounded-full blur-lg opacity-30 animate-pulse"></div>
            <div className="relative bg-white/90 backdrop-blur-sm rounded-full px-8 py-4 border border-primary-200 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer">
              <div className="flex items-center gap-3 text-primary-700 font-semibold">
                <span className="text-2xl"></span>
                <span>Join our community of successful learners</span>
                <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </div>
            </div>
          </div>

          {/* Additional Learning Languages */}
          <div className="mt-8 flex justify-center items-center gap-6 text-sm text-secondary-500">
            <span className="opacity-60">Available in:</span>
            <div className="flex gap-4">
              <span className="bg-white/60 px-3 py-1 rounded-full">English</span>
              <span className="bg-white/60 px-3 py-1 rounded-full">Español</span>
              <span className="bg-white/60 px-3 py-1 rounded-full">Français</span>
              <span className="bg-white/60 px-3 py-1 rounded-full">中文</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
