import React from 'react';
import { Card } from '../ui';
import { features } from '../../data';
import { cn } from '../../utils';

// Professional SVG Icon Components - clean and minimal
const TargetIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const BookIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M8 7h8M8 11h6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

const LightningIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/>
  </svg>
);

const ChartIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="3" y="12" width="4" height="9" fill="currentColor" fillOpacity="0.2" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <rect x="10" y="8" width="4" height="13" fill="currentColor" fillOpacity="0.15" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <rect x="17" y="4" width="4" height="17" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <path d="M3 3v18h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const UsersIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="9" cy="7" r="4" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <circle cx="15" cy="7" r="4" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M1 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    <path d="M15 21v-2a4 4 0 0 1 4-4h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const DeviceIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="5" y="2" width="14" height="20" rx="3" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <rect x="7" y="5" width="10" height="12" fill="white" stroke="currentColor" strokeWidth="1" rx="1"/>
    <circle cx="12" cy="19" r="1.5" fill="currentColor"/>
  </svg>
);

// Icon mapping
const iconMap: { [key: string]: React.ComponentType } = {
  '🎯': TargetIcon,
  '📚': BookIcon,
  '⚡': LightningIcon,
  '📊': ChartIcon,
  '👥': UsersIcon,
  '📱': DeviceIcon,
};

interface FeaturesSectionProps {
  className?: string;
}

export const FeaturesSection: React.FC<FeaturesSectionProps> = ({ className }) => {
  return (
    <section id="features" className={cn('relative py-24 overflow-hidden', className)}>
      {/* Enhanced Thematic Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        {/* English Learning Network Lines */}
        <div className="absolute top-20 left-20 w-48 h-0.5 bg-gradient-to-r from-blue-300 to-transparent opacity-40 transform rotate-45"></div>
        <div className="absolute bottom-32 right-24 w-56 h-0.5 bg-gradient-to-l from-indigo-300 to-transparent opacity-40 transform -rotate-45"></div>
        <div className="absolute top-1/2 left-1/3 w-40 h-0.5 bg-gradient-to-r from-purple-300 to-transparent opacity-40 transform rotate-12"></div>
        <div className="absolute top-1/3 right-1/2 w-44 h-0.5 bg-gradient-to-l from-blue-300 to-transparent opacity-40 transform -rotate-30"></div>
        <div className="absolute bottom-1/3 left-1/4 w-52 h-0.5 bg-gradient-to-r from-indigo-300 to-transparent opacity-40 transform rotate-60"></div>
        <div className="absolute top-1/4 right-1/4 w-36 h-0.5 bg-gradient-to-l from-purple-300 to-transparent opacity-40 transform -rotate-15"></div>

        {/* Skill Mastery Circles */}
        <div className="absolute top-24 right-16 w-36 h-36 border-2 border-blue-200 rounded-full opacity-30"></div>
        <div className="absolute bottom-36 left-24 w-28 h-28 border-2 border-indigo-200 rounded-full opacity-30"></div>
        <div className="absolute top-1/2 right-1/3 w-24 h-24 border-2 border-purple-200 rounded-full opacity-30"></div>
        <div className="absolute top-1/3 left-1/4 w-32 h-32 border-2 border-blue-200 rounded-full opacity-25"></div>

        {/* Learning Progress Squares */}
        <div className="absolute top-1/4 right-1/3 w-20 h-20 bg-blue-100 rounded-lg opacity-35 transform rotate-45"></div>
        <div className="absolute bottom-1/4 left-1/3 w-24 h-24 bg-indigo-100 rounded-lg opacity-35 transform -rotate-12"></div>
        <div className="absolute top-1/3 left-1/2 w-18 h-18 bg-purple-100 rounded-lg opacity-35 transform rotate-30"></div>
        <div className="absolute bottom-1/3 right-1/4 w-22 h-22 bg-blue-100 rounded-lg opacity-30 transform -rotate-45"></div>

        {/* Skill Development Dots */}
        <div className="absolute top-28 left-1/2 w-8 h-8 bg-blue-200 rounded-full opacity-30"></div>
        <div className="absolute top-32 left-1/2 ml-4 w-6 h-6 bg-blue-200 rounded-full opacity-25"></div>
        <div className="absolute top-36 left-1/2 ml-8 w-4 h-4 bg-blue-200 rounded-full opacity-20"></div>

        <div className="absolute bottom-36 right-1/3 w-8 h-8 bg-indigo-200 rounded-full opacity-30"></div>
        <div className="absolute bottom-40 right-1/3 mr-4 w-6 h-6 bg-indigo-200 rounded-full opacity-25"></div>
        <div className="absolute bottom-44 right-1/3 mr-8 w-4 h-4 bg-indigo-200 rounded-full opacity-20"></div>

        <div className="absolute top-1/2 left-20 w-6 h-6 bg-purple-200 rounded-full opacity-30"></div>
        <div className="absolute top-1/2 left-24 w-5 h-5 bg-purple-200 rounded-full opacity-25"></div>
        <div className="absolute top-1/2 left-28 w-4 h-4 bg-purple-200 rounded-full opacity-20"></div>

        {/* Language Mastery Indicators */}
        <div className="absolute top-24 left-1/3 w-2 h-24 bg-gradient-to-b from-blue-200 to-transparent opacity-35 transform rotate-15"></div>
        <div className="absolute bottom-28 right-1/3 w-2 h-28 bg-gradient-to-t from-indigo-200 to-transparent opacity-35 transform -rotate-15"></div>
        <div className="absolute top-1/3 left-20 w-1 h-20 bg-gradient-to-b from-purple-200 to-transparent opacity-35 transform rotate-45"></div>
        <div className="absolute bottom-1/4 right-20 w-2 h-16 bg-gradient-to-t from-blue-200 to-transparent opacity-35 transform -rotate-30"></div>

        {/* Enhanced Floating Elements */}
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/20 to-indigo-300/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-200/15 to-purple-300/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-blue-100/10 to-indigo-200/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Enhanced wave pattern at bottom */}
      <div className="absolute bottom-0 left-0 right-0 h-32 opacity-15">
        <svg className="w-full h-full" viewBox="0 0 1200 120" preserveAspectRatio="none">
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="currentColor" className="text-blue-100"></path>
        </svg>
      </div>

      <div className="container-custom relative z-10">
        {/* Enhanced Professional Section Header */}
        <div className="text-center mb-20">
          {/* Enhanced Badge */}
          <div className="inline-flex items-center px-6 py-3 bg-white/90 backdrop-blur-md rounded-full text-sm font-medium text-blue-700 mb-6 border border-blue-200 shadow-xl">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
            Comprehensive Learning Platform
          </div>

          {/* Main Title - Professional */}
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold font-heading mb-6 leading-tight text-secondary-900">
            Complete English
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Learning Solution
            </span>
          </h2>

          {/* Professional Description */}
          <p className="text-xl md:text-2xl text-secondary-600 max-w-4xl mx-auto leading-relaxed mb-8">
            Master all four essential English skills -
            <span className="font-semibold text-secondary-800"> speaking</span>,
            <span className="font-semibold text-secondary-800"> listening</span>,
            <span className="font-semibold text-secondary-800"> reading</span>, and
            <span className="font-semibold text-secondary-800"> writing</span> -
            through our comprehensive and scientifically-designed curriculum.
          </p>

          {/* Enhanced Professional Stats Row */}
          <div className="flex flex-wrap justify-center gap-8 text-center">
            <div className="flex flex-col bg-white/80 backdrop-blur-md rounded-xl px-6 py-4 shadow-xl border border-white/50 hover:bg-white/90 transition-all duration-300 group">
              <span className="text-2xl font-bold text-blue-600 group-hover:scale-110 transition-transform duration-300">10K+</span>
              <span className="text-sm text-secondary-500">Active Students</span>
            </div>
            <div className="flex flex-col bg-white/80 backdrop-blur-md rounded-xl px-6 py-4 shadow-xl border border-white/50 hover:bg-white/90 transition-all duration-300 group">
              <span className="text-2xl font-bold text-indigo-600 group-hover:scale-110 transition-transform duration-300">95%</span>
              <span className="text-sm text-secondary-500">Success Rate</span>
            </div>
            <div className="flex flex-col bg-white/80 backdrop-blur-md rounded-xl px-6 py-4 shadow-xl border border-white/50 hover:bg-white/90 transition-all duration-300 group">
              <span className="text-2xl font-bold text-purple-600 group-hover:scale-110 transition-transform duration-300">50+</span>
              <span className="text-sm text-secondary-500">Countries</span>
            </div>
          </div>
        </div>

        {/* Professional Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {features.map((feature) => {
            const IconComponent = iconMap[feature.icon] || TargetIcon;

            return (
              <Card
                key={feature.id}
                className="relative text-center hover:shadow-2xl transition-all duration-500 group border-0 bg-white/80 backdrop-blur-sm hover:bg-white hover:scale-105 overflow-hidden"
                variant="default"
              >
                {/* Subtle Background Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-50 transition-opacity duration-500"></div>

                {/* Content */}
                <div className="relative z-10 p-8">
                  {/* Professional Icon Container */}
                  <div className="mb-8">
                    <div className="relative w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-500 shadow-lg group-hover:shadow-xl text-blue-600">
                      <IconComponent />
                      {/* Subtle ring effect */}
                      <div className="absolute inset-0 rounded-2xl bg-blue-200 opacity-0 scale-110 group-hover:opacity-30 group-hover:scale-125 transition-all duration-700"></div>
                    </div>
                  </div>

                  {/* Professional Title */}
                  <h3 className="text-xl md:text-2xl font-bold text-secondary-900 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  {/* Professional Description */}
                  <p className="text-secondary-600 leading-relaxed text-base group-hover:text-secondary-700 transition-colors duration-300">
                    {feature.description}
                  </p>

                  {/* Subtle hover indicator */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Enhanced Professional CTA Section */}
        <div className="text-center mt-20">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full blur-lg opacity-40 animate-pulse"></div>
            <div className="relative inline-flex items-center gap-2 text-secondary-600 text-lg bg-white/90 backdrop-blur-md rounded-full px-8 py-4 shadow-xl border border-white/50 hover:bg-white/95 transition-all duration-300 group cursor-pointer">
              <span>Ready to start your English learning journey?</span>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
