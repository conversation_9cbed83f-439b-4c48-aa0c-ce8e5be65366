import React, { useState } from 'react';
import { Button } from '../ui';
import { cn } from '../../utils';

interface HeroSectionProps {
  className?: string;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  return (
    <section className={cn('relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden', className)}>
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated gradient orbs */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-96 h-96 bg-gradient-to-r from-purple-400/15 to-pink-400/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 left-1/4 w-80 h-80 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }}></div>

        {/* Floating geometric shapes */}
        <div className="absolute top-32 right-1/4 w-4 h-4 bg-blue-500/30 rotate-45 animate-bounce" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-20 w-6 h-6 bg-purple-500/30 rounded-full animate-bounce" style={{ animationDelay: '3s' }}></div>
        <div className="absolute bottom-1/3 right-20 w-5 h-5 bg-cyan-500/30 rotate-12 animate-bounce" style={{ animationDelay: '5s' }}></div>
      </div>

      <div className="container-custom relative z-10 py-20 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Enhanced Content Section */}
          <div className="text-center lg:text-left space-y-8">
            {/* Premium Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-blue-100 to-purple-100 border border-blue-200 text-blue-800 animate-fade-in">
              <svg className="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              #1 ESL Learning Platform
            </div>

            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 font-heading leading-tight animate-fade-in">
              Master English with{' '}
              <span className="relative text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600">
                Confidence
                <svg className="absolute -bottom-4 left-0 w-full h-6" viewBox="0 0 400 20" fill="none">
                  <path d="M3 15C50 5 100 2 200 5C300 8 350 12 397 15" stroke="url(#gradient)" strokeWidth="4" strokeLinecap="round" fill="none"/>
                  <defs>
                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#3b82f6"/>
                      <stop offset="50%" stopColor="#8b5cf6"/>
                      <stop offset="100%" stopColor="#06b6d4"/>
                    </linearGradient>
                  </defs>
                </svg>
              </span>
            </h1>

            <p className="text-xl lg:text-2xl text-gray-600 max-w-2xl leading-relaxed animate-slide-up">
              Join thousands of learners worldwide who have transformed their English skills
              through our comprehensive ESL platform. From beginner to advanced,
              we provide the tools you need to succeed.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start animate-slide-up">
              <Button size="lg" className="group relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                <span className="flex items-center justify-center">
                  Start Learning Now!
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </Button>

              <Button variant="outline" size="lg" className="group relative bg-white/80 backdrop-blur-sm border-2 border-gray-300 hover:border-blue-500 text-gray-700 hover:text-blue-600 font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <span className="flex items-center justify-center">
                  <svg className="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Watch Demo
                </span>
              </Button>
            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-3 gap-8 mt-16 pt-8 border-t border-gray-200/50">
              <div className="text-center lg:text-left group">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">10K+</div>
                <div className="text-sm text-gray-600 font-medium">Active Students</div>
              </div>
              <div className="text-center lg:text-left group">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">95%</div>
                <div className="text-sm text-gray-600 font-medium">Success Rate</div>
              </div>
              <div className="text-center lg:text-left group">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">50+</div>
                <div className="text-sm text-gray-600 font-medium">Countries</div>
              </div>
            </div>
          </div>

          {/* Premium Laptop Mockup with Video */}
          <div className="relative perspective-1000 animate-slide-up">
            {/* Laptop Base/Bottom */}
            <div className="relative">
              {/* Laptop Screen */}
              <div className="relative bg-gray-900 rounded-t-3xl p-6 shadow-2xl transform-gpu hover:scale-105 transition-all duration-500">
                {/* Screen Bezel */}
                <div className="bg-black rounded-t-2xl p-4 relative overflow-hidden">
                  {/* Camera */}
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-700 rounded-full"></div>

                  {/* Video Container */}
                  <div className="relative bg-gray-100 rounded-xl overflow-hidden aspect-video">
                    {!isVideoPlaying ? (
                      // Video Thumbnail with Play Button
                      <div className="relative w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center group cursor-pointer"
                           onClick={() => setIsVideoPlaying(true)}>
                        {/* Video Thumbnail Background */}
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-purple-600/10"></div>

                        {/* Play Button */}
                        <div className="relative z-10 w-20 h-20 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-xl group-hover:scale-110 transition-all duration-300 group-hover:bg-blue-600">
                          <svg className="w-8 h-8 text-blue-600 group-hover:text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                          </svg>
                        </div>

                        {/* Video Title Overlay */}
                        <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3">
                          <h3 className="font-semibold text-gray-900 text-sm">ESL Learning Platform Introduction</h3>
                          <p className="text-xs text-gray-600 mt-1">Discover how our platform transforms English learning</p>
                        </div>

                        {/* Floating UI Elements */}
                        <div className="absolute top-4 right-4 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          LIVE
                        </div>
                      </div>
                    ) : (
                      // Actual YouTube Video
                      <iframe
                        className="w-full h-full rounded-xl"
                        src="https://www.youtube.com/embed/gwIcgyqioPo?autoplay=1&rel=0&modestbranding=1"
                        title="ESL Learning Platform Introduction"
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      ></iframe>
                    )}
                  </div>
                </div>
              </div>

              {/* Laptop Keyboard/Base */}
              <div className="bg-gray-800 rounded-b-3xl px-8 py-6 shadow-2xl">
                {/* Trackpad */}
                <div className="w-32 h-20 bg-gray-700 rounded-xl mx-auto border border-gray-600"></div>
              </div>

              {/* Laptop Shadow */}
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-gray-900/20 to-gray-900/40 rounded-3xl transform translate-y-4 -z-10 blur-xl"></div>
            </div>

            {/* Floating Feature Cards */}
            <div className="absolute -left-8 top-16 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl transform -rotate-6 hover:rotate-0 transition-all duration-300 animate-float">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-sm">Interactive Lessons</div>
                  <div className="text-xs text-gray-600">Learn by doing</div>
                </div>
              </div>
            </div>

            <div className="absolute -right-8 top-32 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl transform rotate-6 hover:rotate-0 transition-all duration-300 animate-float" style={{ animationDelay: '1s' }}>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-sm">Progress Tracking</div>
                  <div className="text-xs text-gray-600">Monitor your growth</div>
                </div>
              </div>
            </div>

            <div className="absolute -left-4 bottom-16 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl transform -rotate-3 hover:rotate-0 transition-all duration-300 animate-float" style={{ animationDelay: '2s' }}>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-sm">Expert Teachers</div>
                  <div className="text-xs text-gray-600">Learn from the best</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
